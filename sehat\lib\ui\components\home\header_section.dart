import 'package:flutter/material.dart';

class HeaderSection extends StatelessWidget {
  final DateTime selectedDate;
  final VoidCallback? onPreviousDay;
  final VoidCallback? onNextDay;

  const HeaderSection({
    super.key,
    required this.selectedDate,
    this.onPreviousDay,
    this.onNextDay,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;


    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Title and AI Coach
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Today',
                style: TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  color: colorScheme.onSurface,
                ),
              ),
              Row(
                children: [
                  // AI Coach badge
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: colorScheme.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.psychology,
                          size: 16,
                          color: colorScheme.primary,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          'AI Coach',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                            color: colorScheme.primary,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 8),
                  // Streak indicator
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
                    decoration: BoxDecoration(
                      color: colorScheme.tertiary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.local_fire_department,
                          size: 16,
                          color: colorScheme.tertiary,
                        ),
                        const SizedBox(width: 2),
                        Text(
                          '1',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                            color: colorScheme.tertiary,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Date navigation
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: colorScheme.surface,
              borderRadius: BorderRadius.circular(25),
              border: Border.all(
                color: colorScheme.outline.withValues(alpha: 0.2),
                width: 1,
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Previous day
                IconButton(
                  onPressed: onPreviousDay,
                  icon: Icon(
                    Icons.chevron_left,
                    color: colorScheme.onSurface.withValues(alpha: 0.6),
                  ),
                  iconSize: 20,
                ),
                
                // Week days
                Expanded(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: _buildWeekDays(context, selectedDate),
                  ),
                ),
                
                // Next day
                IconButton(
                  onPressed: onNextDay,
                  icon: Icon(
                    Icons.chevron_right,
                    color: colorScheme.onSurface.withValues(alpha: 0.6),
                  ),
                  iconSize: 20,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  List<Widget> _buildWeekDays(BuildContext context, DateTime selectedDate) {
    final colorScheme = Theme.of(context).colorScheme;
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    
    // Get the start of the week (Sunday)
    final startOfWeek = selectedDate.subtract(Duration(days: selectedDate.weekday % 7));
    
    return List.generate(7, (index) {
      final date = startOfWeek.add(Duration(days: index));
      final isSelected = date.day == selectedDate.day && 
                        date.month == selectedDate.month && 
                        date.year == selectedDate.year;
      final isToday = date.day == today.day && 
                     date.month == today.month && 
                     date.year == today.year;
      
      final dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
      
      return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            dayNames[index],
            style: TextStyle(
              fontSize: 10,
              color: colorScheme.onSurface.withValues(alpha: 0.6),
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 4),
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: isSelected 
                  ? colorScheme.primary 
                  : isToday 
                      ? colorScheme.primary.withValues(alpha: 0.1)
                      : Colors.transparent,
              borderRadius: BorderRadius.circular(16),
            ),
            child: Center(
              child: Text(
                '${date.day}',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  color: isSelected 
                      ? colorScheme.onPrimary
                      : isToday 
                          ? colorScheme.primary
                          : colorScheme.onSurface,
                ),
              ),
            ),
          ),
        ],
      );
    });
  }
}
