import { app, mongoDb } from "../../instance";
import type { Consumable } from "../../_types/consumable";
import { ObjectId } from "mongodb";

const dbSehat = mongoDb.db('sehat');
const consumablesCollection = dbSehat.collection<Consumable>('consumables');

// GET /consumables - Search and list consumables
app.get('/consumables', async (c) => {
    try {
        const search = c.req.query('q');
        const limit = parseInt(c.req.query('limit') || '50');
        const offset = parseInt(c.req.query('offset') || '0');

        let query = {};
        if (search) {
            query = {
                name: { $regex: search, $options: 'i' }
            };
        }

        const consumables = await consumablesCollection
            .find(query)
            .skip(offset)
            .limit(limit)
            .toArray();

        // Transform data to include id field
        const transformedConsumables = consumables.map(consumable => ({
            ...consumable,
            id: consumable._id?.toString() || consumable.id || '',
        }));

        return c.json({
            success: true,
            data: transformedConsumables,
            total: await consumablesCollection.countDocuments(query)
        });
    } catch (error) {
        console.error('Error fetching consumables:', error);
        return c.json({
            success: false,
            error: 'Failed to fetch consumables'
        }, 500);
    }
});

// GET /consumables/:id - Get specific consumable
app.get('/consumables/:id', async (c) => {
    try {
        const id = c.req.param('id');

        // Try to find by id field first, then by _id
        let consumable = await consumablesCollection.findOne({ id });
        if (!consumable) {
            try {
                consumable = await consumablesCollection.findOne({ _id: new ObjectId(id) });
            } catch (e) {
                // If id is not a valid ObjectId, ignore the error
            }
        }

        if (!consumable) {
            return c.json({
                success: false,
                error: 'Consumable not found'
            }, 404);
        }

        // Transform data to include id field
        const transformedConsumable = {
            ...consumable,
            id: consumable._id?.toString() || consumable.id || '',
        };

        return c.json({
            success: true,
            data: transformedConsumable
        });
    } catch (error) {
        console.error('Error fetching consumable:', error);
        return c.json({
            success: false,
            error: 'Failed to fetch consumable'
        }, 500);
    }
});

