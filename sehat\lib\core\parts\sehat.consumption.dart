import 'package:sehat/c.dart';

class SehatConsumption extends SehatPart {
  SehatConsumption(super.sehat);


  // -------------------------------------------- def
  Future<List<Consumable>> consumableList({String? search, int limit = 50, int offset = 0}) async {
    try {
      final queryParams = <String, String>{
        'limit': limit.toString(),
        'offset': offset.toString(),
      };

      if (search != null && search.isNotEmpty) {
        queryParams['q'] = search;
      }

      final response = await sehat.conn.get('/consumables', queryParams: queryParams);

      if (response['success'] == true) {
        final List<dynamic> data = response['data'];
        return data.map((json) => Consumable.fromJson(json)).toList();
      } else {
        throw Exception(response['error'] ?? 'Failed to fetch consumables');
      }
    } catch (e) {
      throw Exception('Error fetching consumables: $e');
    }
  }

  Future<Consumable?> getConsumable(String id) async {
    try {
      final response = await sehat.conn.get('/consumables/$id');

      if (response['success'] == true) {
        return Consumable.fromJson(response['data']);
      } else {
        return null;
      }
    } catch (e) {
      throw Exception('Error fetching consumable: $e');
    }
  }

  // -------------------------------------------- user consumption
  Future<List<UserConsumtion>> userConsumptionList(String uid, {String? date, int limit = 50, int offset = 0}) async {
    try {
      final queryParams = <String, String>{
        'limit': limit.toString(),
        'offset': offset.toString(),
      };

      if (date != null) {
        queryParams['date'] = date;
      }

      final response = await sehat.conn.get('/user/$uid/consumptions', queryParams: queryParams);

      if (response['success'] == true) {
        final List<dynamic> data = response['data'];
        return data.map((json) => UserConsumtion.fromJson(json)).toList();
      } else {
        throw Exception(response['error'] ?? 'Failed to fetch user consumptions');
      }
    } catch (e) {
      throw Exception('Error fetching user consumptions: $e');
    }
  }

  Future<UserConsumtion> userConsumptionAdd(String uid, String consumableId, double amount, String unit) async {
    try {
      // First get the consumable details
      final consumable = await getConsumable(consumableId);
      if (consumable == null) {
        throw Exception('Consumable not found');
      }

      final data = {
        'type': 'food', // You might want to determine this from consumable
        'name': consumable.name,
        'pic': consumable.pic,
        'calories': consumable.calorie,
        'fat': consumable.fat,
        'carb': consumable.carb,
        'sugar': consumable.sugar,
        'protein': consumable.protein,
        'sodium': consumable.sodium,
        'fiber': consumable.fiber,
        'amount': amount,
        'unit': unit,
      };

      final response = await sehat.conn.post('/user/$uid/consumptions', data);

      if (response['success'] == true) {
        return UserConsumtion.fromJson(response['data']);
      } else {
        throw Exception(response['error'] ?? 'Failed to add consumption');
      }
    } catch (e) {
      throw Exception('Error adding consumption: $e');
    }
  }

  Future<void> userConsumptionDel(String uid, String id) async {
    try {
      final response = await sehat.conn.delete('/user/$uid/consumptions/$id');

      if (response['success'] != true) {
        throw Exception(response['error'] ?? 'Failed to delete consumption');
      }
    } catch (e) {
      throw Exception('Error deleting consumption: $e');
    }
  }
}
