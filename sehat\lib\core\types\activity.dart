class Activity {
  final String id;
  final String name;
  final int calorie; // calorie per minute

  Activity({
    required this.id,
    required this.name,
    required this.calorie,
  });

  factory Activity.fromJson(Map<String, dynamic> json) {
    return Activity(
      id: json['id'] ?? '',
      name: json['name'],
      calorie: (json['calorie'] as num).toInt(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'calorie': calorie,
    };
  }
}
