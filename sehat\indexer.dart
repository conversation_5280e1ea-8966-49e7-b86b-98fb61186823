// ignore_for_file: avoid_print
// Tool to automatically index lib and watch for changes

import 'dart:io';
import 'dart:async';

final loaderFnName = 'loadCMSehat';

int filesCount = 0;
int loadersCount = 0;
bool isWatchMode = false;

void printHelp() {
  print('''
Usage: dart indexer.dart [options]

Options:
  -w, --watch    Watch for file changes and re-index automatically
  -h, --help     Show this help message
''');
}

Future<void> main(List<String> args) async {
  // Check for help flag
  if (args.contains('--help') || args.contains('-h')) {
    printHelp();
    return;
  }

  // Check if watch mode is enabled
  isWatchMode = args.contains('--watch') || args.contains('-w');

  final indexer = Indexer('lib', loaderFnName: loaderFnName);
  await indexer.start();

  print('Total: $filesCount files, $loadersCount loaders');

  if (isWatchMode) {
    print('\nWatching for changes... (Press Ctrl+C to exit)');
    await indexer.watch();
  }
}

final loadRegexp = RegExp(r'(void (\$load|\$def|\$app)\w+)');

class FileIndex {
  final String path;
  final List<String> loaders;

  const FileIndex({required this.path, required this.loaders});
}

class Indexer {
  final String path;
  final String? loaderFnName;
  Indexer(this.path, {this.loaderFnName});
  final List<FileIndex> indexes = [];

  // Store watchers to prevent garbage collection
  final List<StreamSubscription> _watchers = [];

  Future start() async {
    await index(path);

    // sort
    indexes.sort((a, b) => a.path.compareTo(b.path));

    // write file
    final file = File('$path/_load.dart');
    final sb = StringBuffer();
    // exports
    sb.writeln();
    sb.writeln('// ---------------- exports');
    for (var index in indexes) {
      sb.writeln("export r'${index.path}';");
    }

    // loaders
    int loaderCount = 0;
    if (loaderFnName != null) {
      // imports
      sb.writeln();
      sb.writeln('// ---------------- imports');
      sb.writeln("import '_load.dart';");

      sb.writeln();

      sb.writeln('void $loaderFnName() {');
      for (var loader in indexes) {
        for (var l in loader.loaders) {
          loaderCount++;
          sb.writeln(l);
        }
      }

      sb.writeln('}');
    }

    await file.writeAsString(sb.toString());

    filesCount += indexes.length;
    loadersCount += loaderCount;
    print('[$path] ${indexes.length} Files, $loaderCount loaders');
  }

  /// Watch for file changes and re-index when needed
  Future<void> watch() async {
    // Clear any existing watchers
    for (var watcher in _watchers) {
      await watcher.cancel();
    }
    _watchers.clear();

    // Set up a watcher for the main directory
    await _watchDirectory(path);

    // Create a completer that never completes to keep the program running
    final completer = Completer<void>();
    return completer.future;
  }

  /// Watch a directory and all its subdirectories for changes
  Future<void> _watchDirectory(String dirPath) async {
    final dir = Directory(dirPath);
    if (!await dir.exists()) return;

    // Watch the directory and all subdirectories recursively
    final watcher = dir.watch(recursive: true).listen((event) {
      print('changed: ${event.path}');
      _handleFileEvent(event);
    });
    _watchers.add(watcher);
  }

  /// Handle file system events
  void _handleFileEvent(FileSystemEvent event) {
    // Only care about dart files
    if (!event.path.endsWith('.dart')) return;
    if (event.path.endsWith('_load.dart')) return;

    // Debounce events to avoid multiple rapid re-indexing
    _debounceReindex();
  }

  Timer? _debounceTimer;
  void _debounceReindex() {
    if (_debounceTimer?.isActive ?? false) {
      _debounceTimer!.cancel();
    }

    _debounceTimer = Timer(const Duration(milliseconds: 500), () async {
      print('\nRe-indexing due to file changes...');
      // Reset counters
      filesCount = 0;
      loadersCount = 0;
      // Clear existing indexes
      indexes.clear();
      // Re-index
      await start();
      print('\nWatching for changes... (Press Ctrl+C to exit)');
    });
  }

  Future<void> index(String p) async {
    final dir = Directory(p);

    final entries = await dir.list().toList();

    await Future.wait(
      entries.map((e) async {
        final stat = await e.stat();
        if (stat.type == FileSystemEntityType.directory) {
          await index(e.path);
        } else if (e.path.endsWith('.dart')) {
          final filePath = e.path
              .substring(path.length + 1)
              .replaceAll('\\', '/');
          if (filePath == '_load.dart') return;
          // read file content
          final content = await File(e.path).readAsString();
          if (content.contains('// @noindex')) return;

          // if contain part of, skip
          // if (content.contains('part of')) return;
          final matches = loadRegexp.allMatches(content);
          // print('-------- $filePath');

          final List<String> loaders = [];
          for (var m in matches) {
            String match = m.group(0)!.trim();
            if (match.startsWith('void')) {
              match = match.substring('void'.length).trim();
            }
            // print('load $match');
            final loadFn = '  $match();';
            // print('load $loadFn');
            loaders.add(loadFn);
          }

          final fi = FileIndex(path: filePath, loaders: loaders);
          indexes.add(fi);
        }
      }),
    );
  }
}
