import 'package:sehat/c.dart';

class SehatDailyData {
  final Sehat sehat;
  
  SehatDailyData(this.sehat);

  // Cache for daily data to avoid repeated API calls
  final Map<String, DailyData> _cache = {};

  Future<DailyData> getDailyData(String uid, DateTime date) async {
    final dateKey = _formatDate(date);
    
    // Check cache first
    if (_cache.containsKey(dateKey)) {
      return _cache[dateKey]!;
    }

    try {
      // Fetch consumptions and activities for the date
      final consumptionsFuture = sehat.consumption.userConsumptionList(
        uid, 
        date: dateKey,
      );
      
      final activitiesFuture = sehat.activity.userActivitiesList(
        uid, 
        date: dateKey,
      );

      final results = await Future.wait([
        consumptionsFuture,
        activitiesFuture,
      ]);

      final consumptions = results[0] as List<UserConsumtion>;
      final activities = results[1] as List<UserActivity>;

      // Get water intake from local storage or default
      final waterIntake = await _getWaterIntake(uid, date);

      final dailyData = DailyData.fromConsumptionsAndActivities(
        date: date,
        consumptions: consumptions,
        activities: activities,
        waterIntake: waterIntake,
      );

      // Cache the result
      _cache[dateKey] = dailyData;
      
      return dailyData;
    } catch (e) {
      // Return empty data on error
      return DailyData(date: date);
    }
  }

  Future<void> updateWaterIntake(String uid, DateTime date, double intake) async {
    final dateKey = _formatDate(date);
    
    // Update local storage
    await _saveWaterIntake(uid, date, intake);
    
    // Update cache if exists
    if (_cache.containsKey(dateKey)) {
      _cache[dateKey] = _cache[dateKey]!.copyWith(waterIntake: intake);
    }
  }

  Future<void> addConsumption(String uid, UserConsumtion consumption) async {
    final dateKey = _formatDate(consumption.ts!);
    
    // Update cache if exists
    if (_cache.containsKey(dateKey)) {
      final currentData = _cache[dateKey]!;
      final updatedConsumptions = [...currentData.consumptions, consumption];
      
      _cache[dateKey] = DailyData.fromConsumptionsAndActivities(
        date: currentData.date,
        consumptions: updatedConsumptions,
        activities: currentData.activities,
        waterIntake: currentData.waterIntake,
      );
    }
  }

  Future<void> addActivity(String uid, UserActivity activity) async {
    final dateKey = _formatDate(activity.ts);
    
    // Update cache if exists
    if (_cache.containsKey(dateKey)) {
      final currentData = _cache[dateKey]!;
      final updatedActivities = [...currentData.activities, activity];
      
      _cache[dateKey] = DailyData.fromConsumptionsAndActivities(
        date: currentData.date,
        consumptions: currentData.consumptions,
        activities: updatedActivities,
        waterIntake: currentData.waterIntake,
      );
    }
  }

  void clearCache() {
    _cache.clear();
  }

  String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  Future<double> _getWaterIntake(String uid, DateTime date) async {
    // TODO: Implement local storage for water intake
    // For now, return a default value
    return 0.21;
  }

  Future<void> _saveWaterIntake(String uid, DateTime date, double intake) async {
    // TODO: Implement local storage for water intake
    // For now, just log the action
    print('Saving water intake: $intake L for $uid on ${_formatDate(date)}');
  }
}
