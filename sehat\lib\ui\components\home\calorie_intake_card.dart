import 'package:flutter/material.dart';
import 'dart:math' as math;

class CalorieIntakeCard extends StatefulWidget {
  final int consumedCalories;
  final int dailyGoal;
  final int carbs;
  final int carbsGoal;
  final int protein;
  final int proteinGoal;
  final int fat;
  final int fatGoal;
  final VoidCallback? onEditPressed;

  const CalorieIntakeCard({
    super.key,
    this.consumedCalories = 700,
    this.dailyGoal = 2119,
    this.carbs = 2,
    this.carbsGoal = 227,
    this.protein = 63,
    this.proteinGoal = 156,
    this.fat = 50,
    this.fatGoal = 59,
    this.onEditPressed,
  });

  @override
  State<CalorieIntakeCard> createState() => _CalorieIntakeCardState();
}

class _CalorieIntakeCardState extends State<CalorieIntakeCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _progressAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: widget.consumedCalories / widget.dailyGoal,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final caloriesLeft = widget.dailyGoal - widget.consumedCalories;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: colorScheme.outline.withValues(alpha: 0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: colorScheme.shadow.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          // Header
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Calorie Intake',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: colorScheme.onSurface,
                ),
              ),
              Row(
                children: [
                  Text(
                    'Daily Goal',
                    style: TextStyle(
                      fontSize: 12,
                      color: colorScheme.onSurface.withValues(alpha: 0.6),
                    ),
                  ),
                  const SizedBox(width: 4),
                  GestureDetector(
                    onTap: widget.onEditPressed,
                    child: Icon(
                      Icons.edit,
                      size: 16,
                      color: colorScheme.primary,
                    ),
                  ),
                ],
              ),
            ],
          ),
          
          const SizedBox(height: 24),
          
          // Circular progress
          SizedBox(
            width: 160,
            height: 160,
            child: Stack(
              alignment: Alignment.center,
              children: [
                // Background circle
                SizedBox(
                  width: 160,
                  height: 160,
                  child: CircularProgressIndicator(
                    value: 1.0,
                    strokeWidth: 12,
                    backgroundColor: colorScheme.outline.withValues(alpha: 0.1),
                    valueColor: AlwaysStoppedAnimation<Color>(
                      colorScheme.outline.withValues(alpha: 0.1),
                    ),
                  ),
                ),
                
                // Progress circle
                AnimatedBuilder(
                  animation: _progressAnimation,
                  builder: (context, child) {
                    return SizedBox(
                      width: 160,
                      height: 160,
                      child: CircularProgressIndicator(
                        value: _progressAnimation.value,
                        strokeWidth: 12,
                        backgroundColor: Colors.transparent,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          _progressAnimation.value > 1.0
                              ? colorScheme.error
                              : colorScheme.primary,
                        ),
                      ),
                    );
                  },
                ),
                
                // Center content
                Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      '$caloriesLeft',
                      style: TextStyle(
                        fontSize: 32,
                        fontWeight: FontWeight.bold,
                        color: caloriesLeft < 0 ? colorScheme.error : colorScheme.onSurface,
                      ),
                    ),
                    Text(
                      'calories left',
                      style: TextStyle(
                        fontSize: 12,
                        color: colorScheme.onSurface.withValues(alpha: 0.6),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 24),
          
          // Macronutrients
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildMacroColumn(
                context,
                'Carbs',
                widget.carbs,
                widget.carbsGoal,
                'g',
                'carbs',
              ),
              _buildMacroColumn(
                context,
                'Protein',
                widget.protein,
                widget.proteinGoal,
                'g',
                'protein',
              ),
              _buildMacroColumn(
                context,
                'Fat',
                widget.fat,
                widget.fatGoal,
                'g',
                'fat',
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMacroColumn(
    BuildContext context,
    String label,
    int current,
    int goal,
    String unit,
    String macroType,
  ) {
    final colorScheme = Theme.of(context).colorScheme;
    final progress = current / goal;

    // Use theme colors instead of hardcoded colors
    Color macroColor;
    switch (macroType) {
      case 'carbs':
        macroColor = colorScheme.tertiary;
        break;
      case 'protein':
        macroColor = colorScheme.secondary;
        break;
      case 'fat':
        macroColor = colorScheme.primary;
        break;
      default:
        macroColor = colorScheme.primary;
    }

    return Column(
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: colorScheme.onSurface.withValues(alpha: 0.6),
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),

        // Progress bar
        Container(
          width: 60,
          height: 6,
          decoration: BoxDecoration(
            color: colorScheme.outline.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(3),
          ),
          child: FractionallySizedBox(
            alignment: Alignment.centerLeft,
            widthFactor: math.min(progress, 1.0),
            child: Container(
              decoration: BoxDecoration(
                color: macroColor,
                borderRadius: BorderRadius.circular(3),
              ),
            ),
          ),
        ),

        const SizedBox(height: 8),

        Text(
          '$current / $goal$unit',
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w600,
            color: colorScheme.onSurface,
          ),
        ),
      ],
    );
  }
}
