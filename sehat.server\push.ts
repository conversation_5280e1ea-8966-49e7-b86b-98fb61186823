import fs from 'fs/promises';
import { mongoDb } from './src/instance';

const dbSehat = mongoDb.db('sehat');

export async function pushActivities() {
    const col = dbSehat.collection('activities');

    // read push dirs
    const paths = await fs.readdir('push/activities');

    for (const path of paths) {
        console.log('Upload Activities:' + path);
        const file = await fs.readdir('push/' + path);
        const activitiesJSON = await fs.readFile('push/' + path + '/' + file, 'utf8');
        const activities = JSON.parse(activitiesJSON);


        // put into mongodb
        await col.insertMany(activities);

    }
}

export async function pushConsumables() {
    const col = dbSehat.collection('consumables');

    // read push dirs
    const pushDirs = await fs.readdir('push/consumables');

    for (const file of pushDirs) {
        console.log('Upload Consumables:' + file);
        const consumablesJSON = await fs.readFile('push/consumables/' + file, 'utf8');
        const consumables = JSON.parse(consumablesJSON);

        // put into mongodb
        await col.insertMany(consumables);

    }
}

// ------------------------ Run

await pushActivities();
await pushConsumables();