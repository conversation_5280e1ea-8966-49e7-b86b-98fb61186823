["D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\flutter_windows.dll", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\flutter_windows.dll.exp", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\flutter_windows.dll.lib", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\flutter_windows.dll.pdb", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\flutter_export.h", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\flutter_messenger.h", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\flutter_plugin_registrar.h", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\flutter_texture_registrar.h", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\flutter_windows.h", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\icudtl.dat", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\binary_messenger_impl.h", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\byte_buffer_streams.h", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\core_implementations.cc", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\engine_method_result.cc", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\flutter_engine.cc", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\flutter_view_controller.cc", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\basic_message_channel.h", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\binary_messenger.h", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\byte_streams.h", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\dart_project.h", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\encodable_value.h", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\engine_method_result.h", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\event_channel.h", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\event_sink.h", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\event_stream_handler.h", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\event_stream_handler_functions.h", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\flutter_engine.h", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\flutter_view.h", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\flutter_view_controller.h", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\message_codec.h", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\method_call.h", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\method_channel.h", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\method_codec.h", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\method_result.h", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\method_result_functions.h", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\plugin_registrar.h", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\plugin_registrar_windows.h", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\plugin_registry.h", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\standard_codec_serializer.h", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\standard_message_codec.h", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\standard_method_codec.h", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\texture_registrar.h", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\plugin_registrar.cc", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\readme", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\standard_codec.cc", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\texture_registrar_impl.h", "D:\\@c\\app\\cm.sehat\\sehat\\build\\flutter_assets\\kernel_blob.bin", "D:\\@c\\app\\cm.sehat\\sehat\\build\\flutter_assets\\packages/camerawesome/assets/icons/1_1.png", "D:\\@c\\app\\cm.sehat\\sehat\\build\\flutter_assets\\packages/camerawesome/assets/icons/16_9.png", "D:\\@c\\app\\cm.sehat\\sehat\\build\\flutter_assets\\packages/camerawesome/assets/icons/4_3.png", "D:\\@c\\app\\cm.sehat\\sehat\\build\\flutter_assets\\packages/camerawesome/assets/icons/expanded.png", "D:\\@c\\app\\cm.sehat\\sehat\\build\\flutter_assets\\packages/camerawesome/assets/icons/minimized.png", "D:\\@c\\app\\cm.sehat\\sehat\\build\\flutter_assets\\fonts/MaterialIcons-Regular.otf", "D:\\@c\\app\\cm.sehat\\sehat\\build\\flutter_assets\\shaders/ink_sparkle.frag", "D:\\@c\\app\\cm.sehat\\sehat\\build\\flutter_assets\\AssetManifest.json", "D:\\@c\\app\\cm.sehat\\sehat\\build\\flutter_assets\\AssetManifest.bin", "D:\\@c\\app\\cm.sehat\\sehat\\build\\flutter_assets\\FontManifest.json", "D:\\@c\\app\\cm.sehat\\sehat\\build\\flutter_assets\\NOTICES.Z", "D:\\@c\\app\\cm.sehat\\sehat\\build\\flutter_assets\\NativeAssetsManifest.json"]