﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{FF22E972-D14C-37B7-B4A8-B82EC413EEAF}"
	ProjectSection(ProjectDependencies) = postProject
		{F529B5B7-F101-36D0-B33B-B50DDAE94E48} = {F529B5B7-F101-36D0-B33B-B50DDAE94E48}
		{04A96A6A-5E95-3DE8-9531-A58AB9DA445F} = {04A96A6A-5E95-3DE8-9531-A58AB9DA445F}
		{B9C9CAA1-F332-32AB-A61D-CEC479026F8B} = {B9C9CAA1-F332-32AB-A61D-CEC479026F8B}
		{DEBA4780-C680-3320-81B6-BC85588F7B9F} = {DEBA4780-C680-3320-81B6-BC85588F7B9F}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "INSTALL", "INSTALL.vcxproj", "{E0287E2C-E849-3DED-85CF-D5092B43DAC1}"
	ProjectSection(ProjectDependencies) = postProject
		{FF22E972-D14C-37B7-B4A8-B82EC413EEAF} = {FF22E972-D14C-37B7-B4A8-B82EC413EEAF}
		{F529B5B7-F101-36D0-B33B-B50DDAE94E48} = {F529B5B7-F101-36D0-B33B-B50DDAE94E48}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "ZERO_CHECK.vcxproj", "{F529B5B7-F101-36D0-B33B-B50DDAE94E48}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "flutter_assemble", "flutter\flutter_assemble.vcxproj", "{88CB9C48-15E4-3A47-88B7-E0240A9BE6C1}"
	ProjectSection(ProjectDependencies) = postProject
		{F529B5B7-F101-36D0-B33B-B50DDAE94E48} = {F529B5B7-F101-36D0-B33B-B50DDAE94E48}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "flutter_wrapper_app", "flutter\flutter_wrapper_app.vcxproj", "{04A96A6A-5E95-3DE8-9531-A58AB9DA445F}"
	ProjectSection(ProjectDependencies) = postProject
		{F529B5B7-F101-36D0-B33B-B50DDAE94E48} = {F529B5B7-F101-36D0-B33B-B50DDAE94E48}
		{88CB9C48-15E4-3A47-88B7-E0240A9BE6C1} = {88CB9C48-15E4-3A47-88B7-E0240A9BE6C1}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "flutter_wrapper_plugin", "flutter\flutter_wrapper_plugin.vcxproj", "{B9C9CAA1-F332-32AB-A61D-CEC479026F8B}"
	ProjectSection(ProjectDependencies) = postProject
		{F529B5B7-F101-36D0-B33B-B50DDAE94E48} = {F529B5B7-F101-36D0-B33B-B50DDAE94E48}
		{88CB9C48-15E4-3A47-88B7-E0240A9BE6C1} = {88CB9C48-15E4-3A47-88B7-E0240A9BE6C1}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "sehat", "runner\sehat.vcxproj", "{DEBA4780-C680-3320-81B6-BC85588F7B9F}"
	ProjectSection(ProjectDependencies) = postProject
		{F529B5B7-F101-36D0-B33B-B50DDAE94E48} = {F529B5B7-F101-36D0-B33B-B50DDAE94E48}
		{88CB9C48-15E4-3A47-88B7-E0240A9BE6C1} = {88CB9C48-15E4-3A47-88B7-E0240A9BE6C1}
		{04A96A6A-5E95-3DE8-9531-A58AB9DA445F} = {04A96A6A-5E95-3DE8-9531-A58AB9DA445F}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Profile|x64 = Profile|x64
		Release|x64 = Release|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{FF22E972-D14C-37B7-B4A8-B82EC413EEAF}.Debug|x64.ActiveCfg = Debug|x64
		{FF22E972-D14C-37B7-B4A8-B82EC413EEAF}.Debug|x64.Build.0 = Debug|x64
		{FF22E972-D14C-37B7-B4A8-B82EC413EEAF}.Profile|x64.ActiveCfg = Profile|x64
		{FF22E972-D14C-37B7-B4A8-B82EC413EEAF}.Profile|x64.Build.0 = Profile|x64
		{FF22E972-D14C-37B7-B4A8-B82EC413EEAF}.Release|x64.ActiveCfg = Release|x64
		{FF22E972-D14C-37B7-B4A8-B82EC413EEAF}.Release|x64.Build.0 = Release|x64
		{E0287E2C-E849-3DED-85CF-D5092B43DAC1}.Debug|x64.ActiveCfg = Debug|x64
		{E0287E2C-E849-3DED-85CF-D5092B43DAC1}.Debug|x64.Build.0 = Debug|x64
		{E0287E2C-E849-3DED-85CF-D5092B43DAC1}.Profile|x64.ActiveCfg = Profile|x64
		{E0287E2C-E849-3DED-85CF-D5092B43DAC1}.Profile|x64.Build.0 = Profile|x64
		{E0287E2C-E849-3DED-85CF-D5092B43DAC1}.Release|x64.ActiveCfg = Release|x64
		{E0287E2C-E849-3DED-85CF-D5092B43DAC1}.Release|x64.Build.0 = Release|x64
		{F529B5B7-F101-36D0-B33B-B50DDAE94E48}.Debug|x64.ActiveCfg = Debug|x64
		{F529B5B7-F101-36D0-B33B-B50DDAE94E48}.Debug|x64.Build.0 = Debug|x64
		{F529B5B7-F101-36D0-B33B-B50DDAE94E48}.Profile|x64.ActiveCfg = Profile|x64
		{F529B5B7-F101-36D0-B33B-B50DDAE94E48}.Profile|x64.Build.0 = Profile|x64
		{F529B5B7-F101-36D0-B33B-B50DDAE94E48}.Release|x64.ActiveCfg = Release|x64
		{F529B5B7-F101-36D0-B33B-B50DDAE94E48}.Release|x64.Build.0 = Release|x64
		{88CB9C48-15E4-3A47-88B7-E0240A9BE6C1}.Debug|x64.ActiveCfg = Debug|x64
		{88CB9C48-15E4-3A47-88B7-E0240A9BE6C1}.Debug|x64.Build.0 = Debug|x64
		{88CB9C48-15E4-3A47-88B7-E0240A9BE6C1}.Profile|x64.ActiveCfg = Profile|x64
		{88CB9C48-15E4-3A47-88B7-E0240A9BE6C1}.Profile|x64.Build.0 = Profile|x64
		{88CB9C48-15E4-3A47-88B7-E0240A9BE6C1}.Release|x64.ActiveCfg = Release|x64
		{88CB9C48-15E4-3A47-88B7-E0240A9BE6C1}.Release|x64.Build.0 = Release|x64
		{04A96A6A-5E95-3DE8-9531-A58AB9DA445F}.Debug|x64.ActiveCfg = Debug|x64
		{04A96A6A-5E95-3DE8-9531-A58AB9DA445F}.Debug|x64.Build.0 = Debug|x64
		{04A96A6A-5E95-3DE8-9531-A58AB9DA445F}.Profile|x64.ActiveCfg = Profile|x64
		{04A96A6A-5E95-3DE8-9531-A58AB9DA445F}.Profile|x64.Build.0 = Profile|x64
		{04A96A6A-5E95-3DE8-9531-A58AB9DA445F}.Release|x64.ActiveCfg = Release|x64
		{04A96A6A-5E95-3DE8-9531-A58AB9DA445F}.Release|x64.Build.0 = Release|x64
		{B9C9CAA1-F332-32AB-A61D-CEC479026F8B}.Debug|x64.ActiveCfg = Debug|x64
		{B9C9CAA1-F332-32AB-A61D-CEC479026F8B}.Debug|x64.Build.0 = Debug|x64
		{B9C9CAA1-F332-32AB-A61D-CEC479026F8B}.Profile|x64.ActiveCfg = Profile|x64
		{B9C9CAA1-F332-32AB-A61D-CEC479026F8B}.Profile|x64.Build.0 = Profile|x64
		{B9C9CAA1-F332-32AB-A61D-CEC479026F8B}.Release|x64.ActiveCfg = Release|x64
		{B9C9CAA1-F332-32AB-A61D-CEC479026F8B}.Release|x64.Build.0 = Release|x64
		{DEBA4780-C680-3320-81B6-BC85588F7B9F}.Debug|x64.ActiveCfg = Debug|x64
		{DEBA4780-C680-3320-81B6-BC85588F7B9F}.Debug|x64.Build.0 = Debug|x64
		{DEBA4780-C680-3320-81B6-BC85588F7B9F}.Profile|x64.ActiveCfg = Profile|x64
		{DEBA4780-C680-3320-81B6-BC85588F7B9F}.Profile|x64.Build.0 = Profile|x64
		{DEBA4780-C680-3320-81B6-BC85588F7B9F}.Release|x64.ActiveCfg = Release|x64
		{DEBA4780-C680-3320-81B6-BC85588F7B9F}.Release|x64.Build.0 = Release|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {26E9DEF8-EB3F-3900-86B2-FED45F6634E5}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
