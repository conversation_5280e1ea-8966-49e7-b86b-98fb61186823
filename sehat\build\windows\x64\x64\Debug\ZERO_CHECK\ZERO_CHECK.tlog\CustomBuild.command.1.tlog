^D:\@C\APP\CM.SEHAT\SEHAT\BUILD\WINDOWS\X64\CMAKEFILES\37AFAD8D64B25405E8D9D72B0D649F3B\GENERATE.STAMP.RULE
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SD:/@c/app/cm.sehat/sehat/windows -BD:/@c/app/cm.sehat/sehat/build/windows/x64 --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file D:/@c/app/cm.sehat/sehat/build/windows/x64/sehat.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
