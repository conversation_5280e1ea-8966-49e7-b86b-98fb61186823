import 'package:flutter/material.dart';
import 'package:sehat/ui/components/shared/water_glass.dart';

class WaterTrackerSection extends StatefulWidget {
  final double currentIntake;
  final double dailyGoal;
  final Function(double)? onIntakeChanged;

  const WaterTrackerSection({
    super.key,
    this.currentIntake = 0.21,
    this.dailyGoal = 2.00,
    this.onIntakeChanged,
  });

  @override
  State<WaterTrackerSection> createState() => _WaterTrackerSectionState();
}

class _WaterTrackerSectionState extends State<WaterTrackerSection> {
  late List<bool> _glassStates;
  final int _totalGlasses = 12;
  late double _glassCapacity;

  @override
  void initState() {
    super.initState();
    _glassCapacity = widget.dailyGoal / _totalGlasses;
    _updateGlassStates();
  }

  void _updateGlassStates() {
    _glassStates = List.generate(_totalGlasses, (index) {
      final glassThreshold = (index + 1) * _glassCapacity;
      return widget.currentIntake >= glassThreshold;
    });
  }

  void _onGlassTapped(int index) {
    setState(() {
      final wasFilledBefore = _glassStates[index];
      
      if (wasFilledBefore) {
        // If tapping a filled glass, empty it and all glasses after it
        for (int i = index; i < _totalGlasses; i++) {
          _glassStates[i] = false;
        }
      } else {
        // If tapping an empty glass, fill it and all glasses before it
        for (int i = 0; i <= index; i++) {
          _glassStates[i] = true;
        }
      }
      
      // Calculate new intake
      final filledGlasses = _glassStates.where((filled) => filled).length;
      final newIntake = filledGlasses * _glassCapacity;
      
      widget.onIntakeChanged?.call(newIntake);
    });
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: colorScheme.outline.withValues(alpha: 0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: colorScheme.shadow.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Water Tracker',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: colorScheme.onSurface,
                ),
              ),
              Text(
                '${widget.currentIntake.toStringAsFixed(2)} / ${widget.dailyGoal.toStringAsFixed(2)} L',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: colorScheme.primary,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Water glasses grid
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 6,
              crossAxisSpacing: 8,
              mainAxisSpacing: 8,
              childAspectRatio: 1,
            ),
            itemCount: _totalGlasses,
            itemBuilder: (context, index) {
              return WaterGlass(
                isFilled: _glassStates[index],
                onTap: () => _onGlassTapped(index),
                size: 40,
              );
            },
          ),
          
          const SizedBox(height: 16),
          
          // Progress indicator
          Container(
            height: 6,
            decoration: BoxDecoration(
              color: colorScheme.outline.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(3),
            ),
            child: FractionallySizedBox(
              alignment: Alignment.centerLeft,
              widthFactor: (widget.currentIntake / widget.dailyGoal).clamp(0.0, 1.0),
              child: Container(
                decoration: BoxDecoration(
                  color: colorScheme.primary,
                  borderRadius: BorderRadius.circular(3),
                ),
              ),
            ),
          ),
          
          const SizedBox(height: 8),
          
          // Quick add buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildQuickAddButton(
                context,
                '250ml',
                () => _addWater(0.25),
              ),
              _buildQuickAddButton(
                context,
                '500ml',
                () => _addWater(0.5),
              ),
              _buildQuickAddButton(
                context,
                '1L',
                () => _addWater(1.0),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuickAddButton(BuildContext context, String label, VoidCallback onPressed) {
    final colorScheme = Theme.of(context).colorScheme;

    return Expanded(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 4),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: onPressed,
            borderRadius: BorderRadius.circular(12),
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
              decoration: BoxDecoration(
                color: colorScheme.primary.withValues(alpha: 0.08),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: colorScheme.primary.withValues(alpha: 0.2),
                  width: 1,
                ),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    width: 32,
                    height: 32,
                    decoration: BoxDecoration(
                      color: colorScheme.primary.withValues(alpha: 0.15),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Icon(
                      Icons.add,
                      color: colorScheme.primary,
                      size: 18,
                    ),
                  ),
                  const SizedBox(height: 6),
                  Text(
                    label,
                    style: TextStyle(
                      fontSize: 11,
                      fontWeight: FontWeight.w600,
                      color: colorScheme.primary,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _addWater(double amount) {
    final newIntake = (widget.currentIntake + amount).clamp(0.0, widget.dailyGoal);
    widget.onIntakeChanged?.call(newIntake);
    setState(() {
      _updateGlassStates();
    });
  }
}
