export interface UserConsumptionEntry {

    // ------------------------------- consumption info
    type: 'food' | 'drink';
    name: string;
    pic: string;

    calories: number;
    fat?: number;
    carb?: number;
    sugar?: number;
    protein?: number;
    sodium?: number;
    fiber?: number;

    // ------------------------------- user info
    uid: string;
    amount: number;
    unit: number;
}


export interface UserConsumtion extends UserConsumptionEntry {
    _id: string;
    ts: Date;

}

export function userConsume(consumtion: UserConsumptionEntry) {
    const ts = new Date();
    const id = `${consumtion.uid}-${ts.getTime()}`;
    const consumption: UserConsumtion = {
        _id: id,
        ts: ts,
        ...consumtion
    }

}