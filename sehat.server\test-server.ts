// Test script to verify server structure without MongoDB
import { Hono } from "hono";

const app = new Hono();

// Mock the APIs without MongoDB
app.get('/consumables', (c) => {
    return c.json({
        success: true,
        data: [
            {
                id: "test-1",
                name: "Test Food",
                pic: "",
                calorie: 100,
                fat: 5,
                carb: 20,
                sugar: 2,
                protein: 3,
                sodium: 100,
                fiber: 1
            }
        ],
        total: 1
    });
});

app.get('/activities', (c) => {
    return c.json({
        success: true,
        data: [
            {
                id: "test-activity-1",
                name: "Test Activity",
                calorie: 5
            }
        ],
        total: 1
    });
});

app.get('/user/:uid/consumptions', (c) => {
    return c.json({
        success: true,
        data: [],
        total: 0
    });
});

app.get('/user/:uid/activities', (c) => {
    return c.json({
        success: true,
        data: [],
        total: 0
    });
});

console.log('Test server starting on port 3001...');

export default {
    port: 3001,
    fetch: app.fetch,
};
