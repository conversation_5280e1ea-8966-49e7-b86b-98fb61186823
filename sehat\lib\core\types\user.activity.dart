import 'package:sehat/c.dart';

class UserActivity {
  final String id;
  final String uid;
  final DateTime ts;
  final int duration; // in minutes
  final Activity activity;

  UserActivity({
    required this.id,
    required this.uid,
    required this.ts,
    required this.duration,
    required this.activity,
  });

  factory UserActivity.fromJson(Map<String, dynamic> json) {
    return UserActivity(
      id: json['_id'],
      uid: json['uid'],
      ts: DateTime.parse(json['ts']),
      duration: json['duration'],
      activity: Activity.fromJson(json['activity']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'uid': uid,
      'ts': ts.toIso8601String(),
      'duration': duration,
      'activity': activity.toJson(),
    };
  }

  // Convenience getters
  String get name => activity.name;
  int get calorie => activity.calorie;
}
