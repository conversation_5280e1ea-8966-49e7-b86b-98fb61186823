import 'package:sehat/c.dart';

class DailyData {
  final DateTime date;
  final List<UserConsumtion> consumptions;
  final List<UserActivity> activities;
  final double waterIntake;
  final int totalCalories;
  final int totalCarbs;
  final int totalProtein;
  final int totalFat;

  DailyData({
    required this.date,
    this.consumptions = const [],
    this.activities = const [],
    this.waterIntake = 0.0,
    this.totalCalories = 0,
    this.totalCarbs = 0,
    this.totalProtein = 0,
    this.totalFat = 0,
  });

  factory DailyData.fromConsumptionsAndActivities({
    required DateTime date,
    required List<UserConsumtion> consumptions,
    required List<UserActivity> activities,
    double waterIntake = 0.0,
  }) {
    int totalCalories = 0;
    int totalCarbs = 0;
    int totalProtein = 0;
    int totalFat = 0;

    // Calculate totals from consumptions
    for (final consumption in consumptions) {
      totalCalories += consumption.calorie.round();
      totalCarbs += (consumption.carb ?? 0).round();
      totalProtein += (consumption.protein ?? 0).round();
      totalFat += (consumption.fat ?? 0).round();
    }

    // Subtract calories burned from activities
    for (final activity in activities) {
      final caloriesBurned = (activity.activity.calorie * activity.duration);
      totalCalories -= caloriesBurned;
    }

    return DailyData(
      date: date,
      consumptions: consumptions,
      activities: activities,
      waterIntake: waterIntake,
      totalCalories: totalCalories,
      totalCarbs: totalCarbs,
      totalProtein: totalProtein,
      totalFat: totalFat,
    );
  }

  DailyData copyWith({
    DateTime? date,
    List<UserConsumtion>? consumptions,
    List<UserActivity>? activities,
    double? waterIntake,
    int? totalCalories,
    int? totalCarbs,
    int? totalProtein,
    int? totalFat,
  }) {
    return DailyData(
      date: date ?? this.date,
      consumptions: consumptions ?? this.consumptions,
      activities: activities ?? this.activities,
      waterIntake: waterIntake ?? this.waterIntake,
      totalCalories: totalCalories ?? this.totalCalories,
      totalCarbs: totalCarbs ?? this.totalCarbs,
      totalProtein: totalProtein ?? this.totalProtein,
      totalFat: totalFat ?? this.totalFat,
    );
  }
}
