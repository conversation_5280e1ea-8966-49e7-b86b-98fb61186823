import { app, mongoDb } from "../../instance";
import type { Activity } from "../../_types/activity";
import { ObjectId } from "mongodb";

const dbSehat = mongoDb.db('sehat');
const activitiesCollection = dbSehat.collection<Activity>('activities');

// GET /activities - Search and list activities
app.get('/activities', async (c) => {
    try {
        const search = c.req.query('q');
        const limit = parseInt(c.req.query('limit') || '50');
        const offset = parseInt(c.req.query('offset') || '0');

        let query = {};
        if (search) {
            query = {
                name: { $regex: search, $options: 'i' }
            };
        }

        const activities = await activitiesCollection
            .find(query)
            .skip(offset)
            .limit(limit)
            .toArray();

        // Transform data to include id field
        const transformedActivities = activities.map(activity => ({
            ...activity,
            id: activity._id?.toString() || activity.id || '',
        }));

        return c.json({
            success: true,
            data: transformedActivities,
            total: await activitiesCollection.countDocuments(query)
        });
    } catch (error) {
        console.error('Error fetching activities:', error);
        return c.json({
            success: false,
            error: 'Failed to fetch activities'
        }, 500);
    }
});

// GET /activities/:id - Get specific activity
app.get('/activities/:id', async (c) => {
    try {
        const id = c.req.param('id');

        // Try to find by id field first, then by _id
        let activity = await activitiesCollection.findOne({ id });
        if (!activity) {
            try {
                activity = await activitiesCollection.findOne({ _id: new ObjectId(id) });
            } catch (e) {
                // If id is not a valid ObjectId, ignore the error
            }
        }

        if (!activity) {
            return c.json({
                success: false,
                error: 'Activity not found'
            }, 404);
        }

        // Transform data to include id field
        const transformedActivity = {
            ...activity,
            id: activity._id?.toString() || activity.id || '',
        };

        return c.json({
            success: true,
            data: transformedActivity
        });
    } catch (error) {
        console.error('Error fetching activity:', error);
        return c.json({
            success: false,
            error: 'Failed to fetch activity'
        }, 500);
    }
});
