import 'package:flutter/material.dart';

class BtnConsumeDrink extends StatefulWidget {
  final bool isFilled;
  final VoidCallback? onTap;

  const BtnConsumeDrink({
    this.isFilled = false,
    this.onTap,
    super.key,
  });

  @override
  State<BtnConsumeDrink> createState() => _BtnConsumeDrinkState();
}

class _BtnConsumeDrinkState extends State<BtnConsumeDrink>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fillAnimation;
  bool _isCurrentlyFilled = false;

  @override
  void initState() {
    super.initState();
    _isCurrentlyFilled = widget.isFilled;

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fillAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    if (widget.isFilled) {
      _animationController.value = 1.0;
    }
  }

  @override
  void didUpdateWidget(BtnConsumeDrink oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isFilled != oldWidget.isFilled) {
      _isCurrentlyFilled = widget.isFilled;
      if (widget.isFilled) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _handleTap() {
    setState(() {
      _isCurrentlyFilled = !_isCurrentlyFilled;
    });

    if (_isCurrentlyFilled) {
      _animationController.forward();
    } else {
      _animationController.reverse();
    }

    widget.onTap?.call();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: _handleTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            color: colorScheme.surface,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: colorScheme.outline.withValues(alpha: 0.2),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: colorScheme.shadow.withValues(alpha: 0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Center(
            child: AnimatedBuilder(
              animation: _fillAnimation,
              builder: (context, child) {
                return CustomPaint(
                  size: const Size(40, 40),
                  painter: GlassPainter(
                    fillLevel: _fillAnimation.value,
                    primaryColor: colorScheme.primary,
                    surfaceColor: colorScheme.surface,
                    outlineColor: colorScheme.outline,
                  ),
                );
              },
            ),
          ),
        ),
      ),
    );
  }
}

class GlassPainter extends CustomPainter {
  final double fillLevel;
  final Color primaryColor;
  final Color surfaceColor;
  final Color outlineColor;

  GlassPainter({
    required this.fillLevel,
    required this.primaryColor,
    required this.surfaceColor,
    required this.outlineColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0;

    final fillPaint = Paint()
      ..style = PaintingStyle.fill;

    // Glass outline - simple rectangular glass shape
    final glassRect = Rect.fromLTWH(
      size.width * 0.2,
      size.height * 0.1,
      size.width * 0.6,
      size.height * 0.8,
    );

    // Draw glass outline
    paint.color = outlineColor;
    canvas.drawRRect(
      RRect.fromRectAndRadius(glassRect, const Radius.circular(4)),
      paint,
    );

    // Draw glass base (bottom line)
    canvas.drawLine(
      Offset(glassRect.left, glassRect.bottom),
      Offset(glassRect.right, glassRect.bottom),
      paint..strokeWidth = 3.0,
    );

    // Draw water fill if there's any fill level
    if (fillLevel > 0) {
      final waterHeight = glassRect.height * fillLevel;
      final waterRect = Rect.fromLTWH(
        glassRect.left + 2,
        glassRect.bottom - waterHeight,
        glassRect.width - 4,
        waterHeight - 2,
      );

      // Water fill with slight transparency
      fillPaint.color = primaryColor.withValues(alpha: 0.7);
      canvas.drawRRect(
        RRect.fromRectAndRadius(waterRect, const Radius.circular(2)),
        fillPaint,
      );

      // Water surface line (slightly wavy for effect)
      final surfacePaint = Paint()
        ..style = PaintingStyle.stroke
        ..strokeWidth = 1.5
        ..color = primaryColor;

      final surfaceY = waterRect.top;
      final path = Path();
      path.moveTo(waterRect.left, surfaceY);

      // Simple wave effect
      for (double x = waterRect.left; x <= waterRect.right; x += 4) {
        final waveOffset = 1.0 *
            (1 + 0.3 * (x - waterRect.left) / waterRect.width);
        path.lineTo(x, surfaceY + waveOffset);
      }

      canvas.drawPath(path, surfacePaint);
    }

    // Draw glass rim (top edge)
    paint
      ..color = outlineColor
      ..strokeWidth = 2.0;
    canvas.drawLine(
      Offset(glassRect.left, glassRect.top),
      Offset(glassRect.right, glassRect.top),
      paint,
    );
  }

  @override
  bool shouldRepaint(GlassPainter oldDelegate) {
    return oldDelegate.fillLevel != fillLevel ||
        oldDelegate.primaryColor != primaryColor ||
        oldDelegate.surfaceColor != surfaceColor ||
        oldDelegate.outlineColor != outlineColor;
  }
}