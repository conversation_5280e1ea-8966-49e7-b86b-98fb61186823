import 'package:sehat/c.dart';

class SehatActivity extends SehatPart {
  SehatActivity(super.sehat);

  // -------------------------------------------- def
  Future<List<Activity>> activitiesList({String? search, int limit = 50, int offset = 0}) async {
    try {
      final queryParams = <String, String>{
        'limit': limit.toString(),
        'offset': offset.toString(),
      };

      if (search != null && search.isNotEmpty) {
        queryParams['q'] = search;
      }

      final response = await sehat.conn.get('/activities', queryParams: queryParams);

      if (response['success'] == true) {
        final List<dynamic> data = response['data'];
        return data.map((json) => Activity.fromJson(json)).toList();
      } else {
        throw Exception(response['error'] ?? 'Failed to fetch activities');
      }
    } catch (e) {
      throw Exception('Error fetching activities: $e');
    }
  }

  Future<Activity?> getActivity(String id) async {
    try {
      final response = await sehat.conn.get('/activities/$id');

      if (response['success'] == true) {
        return Activity.fromJson(response['data']);
      } else {
        return null;
      }
    } catch (e) {
      throw Exception('Error fetching activity: $e');
    }
  }

  // -------------------------------------------- user activity
  Future<List<UserActivity>> userActivitiesList(String uid, {String? date, int limit = 50, int offset = 0}) async {
    try {
      final queryParams = <String, String>{
        'limit': limit.toString(),
        'offset': offset.toString(),
      };

      if (date != null) {
        queryParams['date'] = date;
      }

      final response = await sehat.conn.get('/user/$uid/activities', queryParams: queryParams);

      if (response['success'] == true) {
        final List<dynamic> data = response['data'];
        return data.map((json) => UserActivity.fromJson(json)).toList();
      } else {
        throw Exception(response['error'] ?? 'Failed to fetch user activities');
      }
    } catch (e) {
      throw Exception('Error fetching user activities: $e');
    }
  }

  Future<UserActivity> userActivityAdd(String uid, String activityId, int durationMinutes) async {
    try {
      // First get the activity details
      final activity = await getActivity(activityId);
      if (activity == null) {
        throw Exception('Activity not found');
      }

      final data = {
        'duration': durationMinutes,
        'activity': {
          'id': activity.id,
          'name': activity.name,
          'calorie': activity.calorie,
        }
      };

      final response = await sehat.conn.post('/user/$uid/activities', data);

      if (response['success'] == true) {
        return UserActivity.fromJson(response['data']);
      } else {
        throw Exception(response['error'] ?? 'Failed to add activity');
      }
    } catch (e) {
      throw Exception('Error adding activity: $e');
    }
  }

  Future<void> userActivityAddByImage(String base64Image) async {
    // TODO: Implement image recognition for activities
    throw UnimplementedError('Image recognition for activities not yet implemented');
  }

  Future<void> userActivityDel(String uid, String id) async {
    try {
      final response = await sehat.conn.delete('/user/$uid/activities/$id');

      if (response['success'] != true) {
        throw Exception(response['error'] ?? 'Failed to delete activity');
      }
    } catch (e) {
      throw Exception('Error deleting activity: $e');
    }
  }
}
