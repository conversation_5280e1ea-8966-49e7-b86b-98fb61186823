import 'package:sehat/c.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  DateTime _selectedDate = DateTime.now();
  int _currentNavIndex = 0;
  DailyData? _dailyData;
  bool _isLoading = false;

  // Mock user ID - in real app this would come from authentication
  final String _uid = 'user123';

  @override
  void initState() {
    super.initState();
    _loadDailyData();
  }

  Future<void> _loadDailyData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final dailyData = await sehat.dailyData.getDailyData(_uid, _selectedDate);
      setState(() {
        _dailyData = dailyData;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _dailyData = DailyData(date: _selectedDate);
        _isLoading = false;
      });
    }
  }

  Future<void> _onDateChanged(DateTime newDate) async {
    setState(() {
      _selectedDate = newDate;
    });
    await _loadDailyData();
  }

  Future<void> _updateWaterIntake(double newIntake) async {
    if (_dailyData != null) {
      setState(() {
        _dailyData = _dailyData!.copyWith(waterIntake: newIntake);
      });

      // Update in backend
      await sehat.dailyData.updateWaterIntake(_uid, _selectedDate, newIntake);
    }
  }

  List<ActivityEntry> _convertToActivityEntries(List<UserActivity> activities) {
    return activities.map((activity) => ActivityEntry(
      name: activity.name,
      duration: activity.duration,
      calories: activity.activity.calorie * activity.duration,
      icon: _getActivityIcon(activity.name),
    )).toList();
  }

  IconData _getActivityIcon(String activityName) {
    final name = activityName.toLowerCase();
    if (name.contains('run') || name.contains('jog')) return Icons.directions_run;
    if (name.contains('walk')) return Icons.directions_walk;
    if (name.contains('bike') || name.contains('cycle')) return Icons.directions_bike;
    if (name.contains('swim')) return Icons.pool;
    if (name.contains('yoga')) return Icons.self_improvement;
    if (name.contains('gym') || name.contains('weight')) return Icons.fitness_center;
    return Icons.sports;
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return Scaffold(
      backgroundColor: colorScheme.surface,
      body: SafeArea(
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    // Header with date navigation
                    HeaderSection(
                      selectedDate: _selectedDate,
                      onPreviousDay: () => _onDateChanged(_selectedDate.subtract(const Duration(days: 1))),
                      onNextDay: () => _onDateChanged(_selectedDate.add(const Duration(days: 1))),
                    ),

                    const SizedBox(height: 8),

                    // Loading indicator or content
                    if (_isLoading)
                      const Center(
                        child: Padding(
                          padding: EdgeInsets.all(32.0),
                          child: CircularProgressIndicator(),
                        ),
                      )
                    else ...[
                      // Calorie intake card
                      CalorieIntakeCard(
                        consumedCalories: _dailyData?.totalCalories ?? 0,
                        carbs: _dailyData?.totalCarbs ?? 0,
                        protein: _dailyData?.totalProtein ?? 0,
                        fat: _dailyData?.totalFat ?? 0,
                        onEditPressed: () {
                          // TODO: Navigate to calorie goal edit screen
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(content: Text('Edit calorie goal')),
                          );
                        },
                      ),
                    
                    const SizedBox(height: 16),
                    
                    // Food intake section
                    FoodIntakeSection(
                      onEatPressed: () => _showMealLog('Eat'),
                      onSnackPressed: () => _showMealLog('Snack'),
                      onDrinkPressed: () => _showMealLog('Drink'),
                    ),
                    
                    const SizedBox(height: 16),
                    
                      // Water tracker section
                      WaterTrackerSection(
                        currentIntake: _dailyData?.waterIntake ?? 0.0,
                        onIntakeChanged: _updateWaterIntake,
                      ),

                      const SizedBox(height: 16),

                      // Activities section
                      ActivitiesSection(
                        activities: _convertToActivityEntries(_dailyData?.activities ?? []),
                        onLogActivities: () {
                          // TODO: Navigate to activity logging screen
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(content: Text('Log activities')),
                          );
                        },
                      ),

                      const SizedBox(height: 100), // Space for bottom navigation
                    ],
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: BottomNavigation(
        currentIndex: _currentNavIndex,
        onTap: (index) {
          setState(() {
            _currentNavIndex = index;
          });
          _handleNavigation(index);
        },
      ),
    );
  }

  void _showMealLog(String mealType) {
    // TODO: Navigate to meal logging screen
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Log $mealType')),
    );
  }

  void _showMealDetails(String mealType) {
    // TODO: Navigate to meal details screen
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('View $mealType details')),
    );
  }

  void _handleNavigation(int index) {
    switch (index) {
      case 0:
        // Already on Calorie screen
        break;
      case 1:
        // TODO: Navigate to Meal Plan
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Navigate to Meal Plan')),
        );
        break;
      case 2:
        // TODO: Show add menu
        _showAddMenu();
        break;
      case 3:
        // TODO: Navigate to Fasting
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Navigate to Fasting')),
        );
        break;
      case 4:
        // TODO: Navigate to My Progress
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Navigate to My Progress')),
        );
        break;
    }
  }

  void _showAddMenu() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        ),
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 20),
            Text(
              'Add New',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 20),
            _buildAddMenuItem(Icons.restaurant, 'Log Food', () {
              Navigator.pop(context);
              _showMealLog('Food');
            }),
            _buildAddMenuItem(Icons.local_drink, 'Log Water', () {
              Navigator.pop(context);
              final currentIntake = _dailyData?.waterIntake ?? 0.0;
              _updateWaterIntake((currentIntake + 0.25).clamp(0.0, 2.0));
            }),
            _buildAddMenuItem(Icons.fitness_center, 'Log Activity', () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Log Activity')),
              );
            }),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildAddMenuItem(IconData icon, String title, VoidCallback onTap) {
    final colorScheme = Theme.of(context).colorScheme;
    
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(12),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border.all(
                color: colorScheme.outline.withValues(alpha: 0.2),
                width: 1,
              ),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                Icon(icon, color: colorScheme.primary),
                const SizedBox(width: 16),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: colorScheme.onSurface,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
