import { app, mongoDb } from "../../instance";
import type { UserActivity } from "../../_types/activity";

const dbSehat = mongoDb.db('sehat');
const userActivitiesCollection = dbSehat.collection<UserActivity>('user_activities');

// GET /user/:uid/activities - Get user's activity history
app.get('/user/:uid/activities', async (c) => {
    try {
        const uid = c.req.param('uid');
        const limit = parseInt(c.req.query('limit') || '50');
        const offset = parseInt(c.req.query('offset') || '0');
        const date = c.req.query('date'); // Optional date filter (YYYY-MM-DD)

        let query: any = { uid };
        
        // Add date filter if provided
        if (date) {
            const startDate = new Date(date);
            const endDate = new Date(date);
            endDate.setDate(endDate.getDate() + 1);
            
            query.ts = {
                $gte: startDate,
                $lt: endDate
            };
        }

        const activities = await userActivitiesCollection
            .find(query)
            .sort({ ts: -1 }) // Most recent first
            .skip(offset)
            .limit(limit)
            .toArray();

        return c.json({
            success: true,
            data: activities,
            total: await userActivitiesCollection.countDocuments(query)
        });
    } catch (error) {
        console.error('Error fetching user activities:', error);
        return c.json({
            success: false,
            error: 'Failed to fetch user activities'
        }, 500);
    }
});

// POST /user/:uid/activities - Add new activity
app.post('/user/:uid/activities', async (c) => {
    try {
        const uid = c.req.param('uid');
        const body = await c.req.json();

        const userActivity: UserActivity = {
            _id: `${uid}-${Date.now()}`,
            uid,
            ts: new Date(),
            duration: body.duration, // in minutes
            activity: {
                id: body.activity.id,
                name: body.activity.name,
                calorie: body.activity.calorie
            }
        };

        await userActivitiesCollection.insertOne(userActivity);

        return c.json({
            success: true,
            data: userActivity
        });
    } catch (error) {
        console.error('Error adding user activity:', error);
        return c.json({
            success: false,
            error: 'Failed to add activity'
        }, 500);
    }
});

// DELETE /user/:uid/activities/:id - Delete activity
app.delete('/user/:uid/activities/:id', async (c) => {
    try {
        const uid = c.req.param('uid');
        const id = c.req.param('id');

        const result = await userActivitiesCollection.deleteOne({ 
            _id: id, 
            uid: uid 
        });

        if (result.deletedCount === 0) {
            return c.json({
                success: false,
                error: 'Activity not found or not owned by user'
            }, 404);
        }

        return c.json({
            success: true,
            message: 'Activity deleted successfully'
        });
    } catch (error) {
        console.error('Error deleting user activity:', error);
        return c.json({
            success: false,
            error: 'Failed to delete activity'
        }, 500);
    }
});
