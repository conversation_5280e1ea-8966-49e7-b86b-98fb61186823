# Prompt to generate activities

```ts
I am writing calorie tracking / fitness app,
generate 1000 indonesian common physical activity / sports
in indonesian language with calorie, with format:
 
interface Activity {
    name: string;

    // calorie per minute of activity
    calorie: number;
}
```

# Prompt to generate consumables

```ts
I am writing calorie tracking / fitness app,
generate 1000 indonesian common food and drink
in indonesian language

add information for 1 serving in grams:
calorie, fat, carb, sugar, protein, sodium, fiber.


with format:
 
interface Consumable {
    name: string;

    // calorie per 100 gram
    calorie: number;
    fat: number;
    carb: number;
    sugar: number;
    protein: number;
    sodium: number;
    fiber: number;
}
```

