{"inputs": ["C:\\dev\\flutter\\packages\\flutter_tools\\lib\\src\\build_system\\targets\\windows.dart", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_windows.dll", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_windows.dll.exp", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_windows.dll.lib", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_windows.dll.pdb", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_export.h", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_messenger.h", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_plugin_registrar.h", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_texture_registrar.h", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_windows.h", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\icudtl.dat", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\binary_messenger_impl.h", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\byte_buffer_streams.h", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\core_implementations.cc", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\engine_method_result.cc", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\flutter_engine.cc", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\flutter_view_controller.cc", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\basic_message_channel.h", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\binary_messenger.h", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\byte_streams.h", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\dart_project.h", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\encodable_value.h", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\engine_method_result.h", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\event_channel.h", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\event_sink.h", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\event_stream_handler.h", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\event_stream_handler_functions.h", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\flutter_engine.h", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\flutter_view.h", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\flutter_view_controller.h", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\message_codec.h", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\method_call.h", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\method_channel.h", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\method_codec.h", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\method_result.h", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\method_result_functions.h", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\plugin_registrar.h", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\plugin_registrar_windows.h", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\plugin_registry.h", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\standard_codec_serializer.h", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\standard_message_codec.h", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\standard_method_codec.h", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\texture_registrar.h", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\plugin_registrar.cc", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\readme", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\standard_codec.cc", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\texture_registrar_impl.h"], "outputs": ["D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\flutter_windows.dll", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\flutter_windows.dll.exp", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\flutter_windows.dll.lib", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\flutter_windows.dll.pdb", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\flutter_export.h", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\flutter_messenger.h", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\flutter_plugin_registrar.h", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\flutter_texture_registrar.h", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\flutter_windows.h", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\icudtl.dat", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\binary_messenger_impl.h", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\byte_buffer_streams.h", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\core_implementations.cc", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\engine_method_result.cc", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\flutter_engine.cc", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\flutter_view_controller.cc", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\basic_message_channel.h", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\binary_messenger.h", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\byte_streams.h", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\dart_project.h", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\encodable_value.h", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\engine_method_result.h", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\event_channel.h", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\event_sink.h", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\event_stream_handler.h", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\event_stream_handler_functions.h", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\flutter_engine.h", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\flutter_view.h", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\flutter_view_controller.h", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\message_codec.h", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\method_call.h", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\method_channel.h", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\method_codec.h", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\method_result.h", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\method_result_functions.h", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\plugin_registrar.h", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\plugin_registrar_windows.h", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\plugin_registry.h", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\standard_codec_serializer.h", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\standard_message_codec.h", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\standard_method_codec.h", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\texture_registrar.h", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\plugin_registrar.cc", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\readme", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\standard_codec.cc", "D:\\@c\\app\\cm.sehat\\sehat\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\texture_registrar_impl.h"]}