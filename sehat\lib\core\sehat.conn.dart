import 'package:sehat/c.dart';
import 'dart:convert';
import 'dart:io';

class SehatConn {
  final Sehat sehat;
  final String baseUrl;
  final HttpClient _httpClient = HttpClient();

  SehatConn(this.sehat, {this.baseUrl = 'http://localhost:3000'});

  Future<Map<String, dynamic>> get(String path, {Map<String, String>? queryParams}) async {
    try {
      var uri = Uri.parse('$baseUrl$path');
      if (queryParams != null) {
        uri = uri.replace(queryParameters: queryParams);
      }

      final request = await _httpClient.getUrl(uri);
      request.headers.set('Content-Type', 'application/json');

      final response = await request.close();
      final responseBody = await response.transform(utf8.decoder).join();

      return json.decode(responseBody);
    } catch (e) {
      throw Exception('GET request failed: $e');
    }
  }

  Future<Map<String, dynamic>> post(String path, Map<String, dynamic> data) async {
    try {
      final uri = Uri.parse('$baseUrl$path');
      final request = await _httpClient.postUrl(uri);
      request.headers.set('Content-Type', 'application/json');

      final jsonData = json.encode(data);
      request.write(jsonData);

      final response = await request.close();
      final responseBody = await response.transform(utf8.decoder).join();

      return json.decode(responseBody);
    } catch (e) {
      throw Exception('POST request failed: $e');
    }
  }

  Future<Map<String, dynamic>> delete(String path) async {
    try {
      final uri = Uri.parse('$baseUrl$path');
      final request = await _httpClient.deleteUrl(uri);
      request.headers.set('Content-Type', 'application/json');

      final response = await request.close();
      final responseBody = await response.transform(utf8.decoder).join();

      return json.decode(responseBody);
    } catch (e) {
      throw Exception('DELETE request failed: $e');
    }
  }

  void dispose() {
    _httpClient.close();
  }
}
