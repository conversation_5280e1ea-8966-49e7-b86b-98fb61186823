class Consumable {
  final String id;
  final String name;
  final String pic;
  final double calorie;
  final double? fat;
  final double? carb;
  final double? sugar;
  final double? protein;
  final double? sodium;
  final double? fiber;

  Consumable({
    required this.id,
    required this.name,
    required this.pic,
    required this.calorie,
    required this.fat,
    required this.carb,
    required this.sugar,
    required this.protein,
    required this.sodium,
    required this.fiber,
  });

  factory Consumable.fromJson(Map<String, dynamic> json) {
    return Consumable(
      id: json['id'] ?? '',
      name: json['name'],
      pic: json['pic'] ?? '',
      calorie: (json['calorie'] as num?)?.toDouble() ?? 0.0,
      fat: (json['fat'] as num?)?.toDouble(),
      carb: (json['carb'] as num?)?.toDouble(),
      sugar: (json['sugar'] as num?)?.toDouble(),
      protein: (json['protein'] as num?)?.toDouble(),
      sodium: (json['sodium'] as num?)?.toDouble(),
      fiber: (json['fiber'] as num?)?.toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'pic': pic,
      'calorie': calorie,
      'fat': fat,
      'carb': carb,
      'sugar': sugar,
      'protein': protein,
      'sodium': sodium,
      'fiber': fiber,
    };
  }
}
