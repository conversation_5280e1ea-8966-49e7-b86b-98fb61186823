import { app, mongoDb } from "../../instance";
import type { UserConsumtion } from "../../_types/consumtion";

const dbSehat = mongoDb.db('sehat');
const userConsumptionsCollection = dbSehat.collection<UserConsumtion>('user_consumptions');

// GET /user/:uid/consumptions - Get user's consumption history
app.get('/user/:uid/consumptions', async (c) => {
    try {
        const uid = c.req.param('uid');
        const limit = parseInt(c.req.query('limit') || '50');
        const offset = parseInt(c.req.query('offset') || '0');
        const date = c.req.query('date'); // Optional date filter (YYYY-MM-DD)

        let query: any = { uid };
        
        // Add date filter if provided
        if (date) {
            const startDate = new Date(date);
            const endDate = new Date(date);
            endDate.setDate(endDate.getDate() + 1);
            
            query.ts = {
                $gte: startDate,
                $lt: endDate
            };
        }

        const consumptions = await userConsumptionsCollection
            .find(query)
            .sort({ ts: -1 }) // Most recent first
            .skip(offset)
            .limit(limit)
            .toArray();

        return c.json({
            success: true,
            data: consumptions,
            total: await userConsumptionsCollection.countDocuments(query)
        });
    } catch (error) {
        console.error('Error fetching user consumptions:', error);
        return c.json({
            success: false,
            error: 'Failed to fetch user consumptions'
        }, 500);
    }
});

// POST /user/:uid/consumptions - Add new consumption
app.post('/user/:uid/consumptions', async (c) => {
    try {
        const uid = c.req.param('uid');
        const body = await c.req.json();

        const consumption: UserConsumtion = {
            _id: `${uid}-${Date.now()}`,
            uid,
            ts: new Date(),
            type: body.type,
            name: body.name,
            pic: body.pic,
            calories: body.calories,
            fat: body.fat,
            carb: body.carb,
            sugar: body.sugar,
            protein: body.protein,
            sodium: body.sodium,
            fiber: body.fiber,
            amount: body.amount,
            unit: body.unit
        };

        await userConsumptionsCollection.insertOne(consumption);

        return c.json({
            success: true,
            data: consumption
        });
    } catch (error) {
        console.error('Error adding user consumption:', error);
        return c.json({
            success: false,
            error: 'Failed to add consumption'
        }, 500);
    }
});

// DELETE /user/:uid/consumptions/:id - Delete consumption
app.delete('/user/:uid/consumptions/:id', async (c) => {
    try {
        const uid = c.req.param('uid');
        const id = c.req.param('id');

        const result = await userConsumptionsCollection.deleteOne({ 
            _id: id, 
            uid: uid 
        });

        if (result.deletedCount === 0) {
            return c.json({
                success: false,
                error: 'Consumption not found or not owned by user'
            }, 404);
        }

        return c.json({
            success: true,
            message: 'Consumption deleted successfully'
        });
    } catch (error) {
        console.error('Error deleting user consumption:', error);
        return c.json({
            success: false,
            error: 'Failed to delete consumption'
        }, 500);
    }
});
