[{"name": "<PERSON><PERSON> (Fried Rice)", "serving_size_grams": 150, "calorie": 250, "fat": 10, "carb": 30, "sugar": 5, "protein": 8, "sodium": 500, "fiber": 2}, {"name": "<PERSON><PERSON> (Fried Noodles)", "serving_size_grams": 150, "calorie": 280, "fat": 12, "carb": 35, "sugar": 6, "protein": 7, "sodium": 600, "fiber": 3}, {"name": "Gado-Gado (Indonesian Salad with Peanut Sauce)", "serving_size_grams": 200, "calorie": 300, "fat": 20, "carb": 25, "sugar": 8, "protein": 10, "sodium": 400, "fiber": 5}, {"name": "<PERSON><PERSON> (Chicken Satay)", "serving_size_grams": 100, "calorie": 200, "fat": 12, "carb": 5, "sugar": 2, "protein": 15, "sodium": 350, "fiber": 1}, {"name": "<PERSON> (Chicken Soup)", "serving_size_grams": 250, "calorie": 150, "fat": 8, "carb": 10, "sugar": 3, "protein": 10, "sodium": 700, "fiber": 2}, {"name": "<PERSON><PERSON><PERSON> (Meatball Soup)", "serving_size_grams": 200, "calorie": 220, "fat": 15, "carb": 10, "sugar": 4, "protein": 12, "sodium": 800, "fiber": 1}, {"name": "<PERSON><PERSON><PERSON> (Sweet Martabak)", "serving_size_grams": 100, "calorie": 300, "fat": 15, "carb": 40, "sugar": 20, "protein": 5, "sodium": 150, "fiber": 1}, {"name": "Rendang (Beef <PERSON>)", "serving_size_grams": 100, "calorie": 250, "fat": 20, "carb": 8, "sugar": 2, "protein": 12, "sodium": 400, "fiber": 2}, {"name": "<PERSON><PERSON><PERSON> (Young Jackfruit Stew)", "serving_size_grams": 150, "calorie": 200, "fat": 8, "carb": 25, "sugar": 10, "protein": 5, "sodium": 300, "fiber": 3}, {"name": "<PERSON><PERSON> (<PERSON><PERSON>)", "serving_size_grams": 200, "calorie": 180, "fat": 10, "carb": 12, "sugar": 3, "protein": 10, "sodium": 650, "fiber": 2}, {"name": "<PERSON><PERSON> (Fried Chicken)", "serving_size_grams": 100, "calorie": 230, "fat": 15, "carb": 8, "sugar": 1, "protein": 15, "sodium": 300, "fiber": 0}, {"name": "<PERSON><PERSON> (Grilled Fish)", "serving_size_grams": 120, "calorie": 180, "fat": 8, "carb": 2, "sugar": 0, "protein": 25, "sodium": 200, "fiber": 0}, {"name": "<PERSON><PERSON> (Fried Tofu)", "serving_size_grams": 80, "calorie": 120, "fat": 8, "carb": 3, "sugar": 1, "protein": 8, "sodium": 100, "fiber": 1}, {"name": "<PERSON><PERSON><PERSON> (Fried Tempeh)", "serving_size_grams": 80, "calorie": 150, "fat": 10, "carb": 5, "sugar": 1, "protein": 10, "sodium": 100, "fiber": 2}, {"name": "<PERSON><PERSON> (Sour Vegetable Soup)", "serving_size_grams": 200, "calorie": 80, "fat": 3, "carb": 12, "sugar": 5, "protein": 3, "sodium": 400, "fiber": 4}, {"name": "<PERSON><PERSON> (Oxtail Soup)", "serving_size_grams": 250, "calorie": 250, "fat": 15, "carb": 10, "sugar": 2, "protein": 15, "sodium": 700, "fiber": 1}, {"name": "<PERSON><PERSON> (Cirebon Beef Soup)", "serving_size_grams": 200, "calorie": 280, "fat": 20, "carb": 8, "sugar": 2, "protein": 15, "sodium": 600, "fiber": 1}, {"name": "<PERSON><PERSON> (Coconut Rice)", "serving_size_grams": 150, "calorie": 220, "fat": 10, "carb": 30, "sugar": 3, "protein": 4, "sodium": 250, "fiber": 1}, {"name": "<PERSON><PERSON><PERSON> (Chicken Porridge)", "serving_size_grams": 200, "calorie": 180, "fat": 7, "carb": 20, "sugar": 2, "protein": 8, "sodium": 500, "fiber": 2}, {"name": "Siomay (Steamed Fish Dumplings)", "serving_size_grams": 150, "calorie": 250, "fat": 15, "carb": 20, "sugar": 5, "protein": 10, "sodium": 450, "fiber": 3}, {"name": "Batagor (Fried Tofu and Fish Cake)", "serving_size_grams": 120, "calorie": 280, "fat": 18, "carb": 22, "sugar": 6, "protein": 10, "sodium": 500, "fiber": 2}, {"name": "Pempek (Fish Cake with <PERSON><PERSON><PERSON>)", "serving_size_grams": 100, "calorie": 180, "fat": 8, "carb": 20, "sugar": 3, "protein": 8, "sodium": 350, "fiber": 1}, {"name": "Lontong Sayur (Rice Cake with Vegetable Curry)", "serving_size_grams": 200, "calorie": 250, "fat": 12, "carb": 30, "sugar": 5, "protein": 8, "sodium": 550, "fiber": 4}, {"name": "Ketoprak (Tofu, Rice Cake, and <PERSON><PERSON><PERSON><PERSON> with Peanut Sauce)", "serving_size_grams": 180, "calorie": 320, "fat": 20, "carb": 30, "sugar": 7, "protein": 10, "sodium": 400, "fiber": 5}, {"name": "Asinan (Pickled Vegetables or Fruits)", "serving_size_grams": 150, "calorie": 120, "fat": 5, "carb": 18, "sugar": 8, "protein": 2, "sodium": 300, "fiber": 3}, {"name": "<PERSON><PERSON> (Sweet Iced Tea)", "serving_size_grams": 250, "calorie": 100, "fat": 0, "carb": 25, "sugar": 25, "protein": 0, "sodium": 10, "fiber": 0}, {"name": "<PERSON><PERSON> (Coffee)", "serving_size_grams": 200, "calorie": 2, "fat": 0, "carb": 0, "sugar": 0, "protein": 0, "sodium": 5, "fiber": 0}, {"name": "Air Mineral (Mineral Water)", "serving_size_grams": 250, "calorie": 0, "fat": 0, "carb": 0, "sugar": 0, "protein": 0, "sodium": 0, "fiber": 0}, {"name": "<PERSON><PERSON> (Orange Juice)", "serving_size_grams": 250, "calorie": 120, "fat": 0, "carb": 30, "sugar": 25, "protein": 1, "sodium": 15, "fiber": 1}, {"name": "<PERSON><PERSON> (Herbal Drink)", "serving_size_grams": 150, "calorie": 50, "fat": 0, "carb": 12, "sugar": 8, "protein": 1, "sodium": 20, "fiber": 1}, {"name": "Kle<PERSON>n", "serving_size_grams": 50, "calorie": 100, "fat": 2, "carb": 20, "sugar": 10, "protein": 1, "sodium": 10, "fiber": 1}, {"name": "Onde-Onde", "serving_size_grams": 60, "calorie": 150, "fat": 8, "carb": 18, "sugar": 6, "protein": 3, "sodium": 20, "fiber": 1}, {"name": "Nagasari", "serving_size_grams": 80, "calorie": 120, "fat": 3, "carb": 22, "sugar": 8, "protein": 2, "sodium": 15, "fiber": 1}, {"name": "Getuk", "serving_size_grams": 70, "calorie": 110, "fat": 1, "carb": 25, "sugar": 12, "protein": 1, "sodium": 10, "fiber": 2}, {"name": "<PERSON><PERSON>l", "serving_size_grams": 50, "calorie": 80, "fat": 0, "carb": 20, "sugar": 10, "protein": 0, "sodium": 5, "fiber": 0}, {"name": "<PERSON><PERSON>", "serving_size_grams": 70, "calorie": 130, "fat": 5, "carb": 18, "sugar": 8, "protein": 2, "sodium": 20, "fiber": 1}, {"name": "Kue Lumpur", "serving_size_grams": 60, "calorie": 140, "fat": 6, "carb": 20, "sugar": 10, "protein": 2, "sodium": 25, "fiber": 0}, {"name": "<PERSON><PERSON>", "serving_size_grams": 50, "calorie": 110, "fat": 4, "carb": 18, "sugar": 7, "protein": 2, "sodium": 15, "fiber": 1}, {"name": "<PERSON><PERSON>", "serving_size_grams": 60, "calorie": 100, "fat": 2, "carb": 18, "sugar": 6, "protein": 2, "sodium": 10, "fiber": 0}, {"name": "Put<PERSON> Ayu", "serving_size_grams": 50, "calorie": 90, "fat": 3, "carb": 15, "sugar": 5, "protein": 1, "sodium": 10, "fiber": 0}, {"name": "<PERSON><PERSON>", "serving_size_grams": 80, "calorie": 200, "fat": 10, "carb": 25, "sugar": 12, "protein": 3, "sodium": 30, "fiber": 1}, {"name": "<PERSON><PERSON>", "serving_size_grams": 40, "calorie": 80, "fat": 4, "carb": 10, "sugar": 5, "protein": 1, "sodium": 5, "fiber": 0}, {"name": "<PERSON><PERSON><PERSON>", "serving_size_grams": 60, "calorie": 120, "fat": 4, "carb": 20, "sugar": 8, "protein": 1, "sodium": 10, "fiber": 1}, {"name": "<PERSON><PERSON><PERSON>", "serving_size_grams": 50, "calorie": 100, "fat": 3, "carb": 18, "sugar": 7, "protein": 1, "sodium": 5, "fiber": 1}, {"name": "Pastel", "serving_size_grams": 70, "calorie": 180, "fat": 10, "carb": 15, "sugar": 2, "protein": 5, "sodium": 150, "fiber": 1}, {"name": "Lumpia", "serving_size_grams": 80, "calorie": 200, "fat": 12, "carb": 18, "sugar": 3, "protein": 6, "sodium": 200, "fiber": 2}, {"name": "Bakwan", "serving_size_grams": 60, "calorie": 120, "fat": 7, "carb": 10, "sugar": 1, "protein": 3, "sodium": 100, "fiber": 1}, {"name": "<PERSON><PERSON>", "serving_size_grams": 70, "calorie": 150, "fat": 9, "carb": 10, "sugar": 2, "protein": 4, "sodium": 120, "fiber": 1}, {"name": "C<PERSON>ng", "serving_size_grams": 50, "calorie": 100, "fat": 2, "carb": 20, "sugar": 1, "protein": 1, "sodium": 50, "fiber": 0}, {"name": "<PERSON><PERSON><PERSON>", "serving_size_grams": 60, "calorie": 130, "fat": 8, "carb": 8, "sugar": 1, "protein": 5, "sodium": 110, "fiber": 1}, {"name": "Combro", "serving_size_grams": 60, "calorie": 140, "fat": 8, "carb": 12, "sugar": 2, "protein": 3, "sodium": 90, "fiber": 2}, {"name": "<PERSON><PERSON><PERSON>", "serving_size_grams": 50, "calorie": 120, "fat": 7, "carb": 10, "sugar": 5, "protein": 2, "sodium": 80, "fiber": 1}, {"name": "<PERSON><PERSON><PERSON>", "serving_size_grams": 60, "calorie": 150, "fat": 9, "carb": 12, "sugar": 1, "protein": 4, "sodium": 120, "fiber": 1}, {"name": "Risoles", "serving_size_grams": 70, "calorie": 180, "fat": 11, "carb": 15, "sugar": 2, "protein": 5, "sodium": 150, "fiber": 1}, {"name": "<PERSON><PERSON>", "serving_size_grams": 80, "calorie": 160, "fat": 6, "carb": 20, "sugar": 1, "protein": 5, "sodium": 100, "fiber": 1}, {"name": "Arem-<PERSON><PERSON>", "serving_size_grams": 100, "calorie": 200, "fat": 8, "carb": 25, "sugar": 2, "protein": 6, "sodium": 120, "fiber": 2}, {"name": "<PERSON><PERSON>", "serving_size_grams": 150, "calorie": 250, "fat": 12, "carb": 30, "sugar": 3, "protein": 5, "sodium": 300, "fiber": 2}, {"name": "<PERSON><PERSON>", "serving_size_grams": 180, "calorie": 300, "fat": 15, "carb": 35, "sugar": 4, "protein": 8, "sodium": 400, "fiber": 3}, {"name": "<PERSON><PERSON>", "serving_size_grams": 160, "calorie": 240, "fat": 10, "carb": 32, "sugar": 4, "protein": 6, "sodium": 350, "fiber": 4}, {"name": "Doclang", "serving_size_grams": 170, "calorie": 280, "fat": 14, "carb": 30, "sugar": 6, "protein": 8, "sodium": 400, "fiber": 5}, {"name": "<PERSON><PERSON>", "serving_size_grams": 200, "calorie": 260, "fat": 12, "carb": 25, "sugar": 5, "protein": 10, "sodium": 500, "fiber": 3}, {"name": "<PERSON><PERSON><PERSON>", "serving_size_grams": 150, "calorie": 220, "fat": 15, "carb": 15, "sugar": 7, "protein": 8, "sodium": 400, "fiber": 4}, {"name": "<PERSON><PERSON>", "serving_size_grams": 180, "calorie": 250, "fat": 16, "carb": 20, "sugar": 5, "protein": 8, "sodium": 450, "fiber": 5}, {"name": "<PERSON><PERSON><PERSON>", "serving_size_grams": 220, "calorie": 300, "fat": 18, "carb": 28, "sugar": 6, "protein": 10, "sodium": 600, "fiber": 3}, {"name": "<PERSON><PERSON>", "serving_size_grams": 200, "calorie": 280, "fat": 16, "carb": 25, "sugar": 5, "protein": 12, "sodium": 550, "fiber": 2}, {"name": "Seblak", "serving_size_grams": 150, "calorie": 200, "fat": 10, "carb": 22, "sugar": 4, "protein": 6, "sodium": 400, "fiber": 3}, {"name": "<PERSON>", "serving_size_grams": 250, "calorie": 320, "fat": 22, "carb": 15, "sugar": 3, "protein": 15, "sodium": 700, "fiber": 1}, {"name": "<PERSON><PERSON>", "serving_size_grams": 220, "calorie": 280, "fat": 18, "carb": 12, "sugar": 2, "protein": 16, "sodium": 650, "fiber": 1}, {"name": "<PERSON><PERSON><PERSON>", "serving_size_grams": 200, "calorie": 350, "fat": 25, "carb": 10, "sugar": 2, "protein": 20, "sodium": 700, "fiber": 1}, {"name": "Pallubasa", "serving_size_grams": 180, "calorie": 300, "fat": 20, "carb": 8, "sugar": 1, "protein": 18, "sodium": 600, "fiber": 0}, {"name": "Coto Makassar", "serving_size_grams": 200, "calorie": 320, "fat": 22, "carb": 10, "sugar": 1, "protein": 20, "sodium": 650, "fiber": 0}, {"name": "<PERSON><PERSON>", "serving_size_grams": 200, "calorie": 350, "fat": 20, "carb": 35, "sugar": 5, "protein": 10, "sodium": 700, "fiber": 4}, {"name": "<PERSON><PERSON><PERSON>", "serving_size_grams": 150, "calorie": 260, "fat": 14, "carb": 30, "sugar": 4, "protein": 6, "sodium": 550, "fiber": 2}, {"name": "Cap Cai", "serving_size_grams": 200, "calorie": 150, "fat": 8, "carb": 15, "sugar": 5, "protein": 5, "sodium": 350, "fiber": 5}, {"name": "<PERSON>", "serving_size_grams": 180, "calorie": 280, "fat": 18, "carb": 15, "sugar": 3, "protein": 12, "sodium": 450, "fiber": 2}, {"name": "<PERSON><PERSON>", "serving_size_grams": 120, "calorie": 220, "fat": 12, "carb": 10, "sugar": 5, "protein": 18, "sodium": 400, "fiber": 1}, {"name": "<PERSON><PERSON>", "serving_size_grams": 200, "calorie": 180, "fat": 10, "carb": 12, "sugar": 4, "protein": 8, "sodium": 300, "fiber": 3}, {"name": "<PERSON><PERSON>", "serving_size_grams": 150, "calorie": 250, "fat": 14, "carb": 18, "sugar": 8, "protein": 15, "sodium": 350, "fiber": 1}, {"name": "<PERSON><PERSON><PERSON>", "serving_size_grams": 120, "calorie": 200, "fat": 10, "carb": 15, "sugar": 7, "protein": 12, "sodium": 300, "fiber": 1}, {"name": "<PERSON><PERSON>", "serving_size_grams": 150, "calorie": 80, "fat": 5, "carb": 5, "sugar": 2, "protein": 3, "sodium": 200, "fiber": 3}, {"name": "<PERSON><PERSON>", "serving_size_grams": 150, "calorie": 90, "fat": 6, "carb": 7, "sugar": 3, "protein": 3, "sodium": 220, "fiber": 4}, {"name": "Oseng-Oseng Tempe", "serving_size_grams": 100, "calorie": 160, "fat": 10, "carb": 8, "sugar": 2, "protein": 10, "sodium": 250, "fiber": 3}, {"name": "<PERSON><PERSON><PERSON>", "serving_size_grams": 120, "calorie": 150, "fat": 8, "carb": 5, "sugar": 1, "protein": 10, "sodium": 200, "fiber": 2}, {"name": "<PERSON><PERSON><PERSON>", "serving_size_grams": 150, "calorie": 180, "fat": 10, "carb": 3, "sugar": 0, "protein": 20, "sodium": 250, "fiber": 1}, {"name": "Botok", "serving_size_grams": 100, "calorie": 140, "fat": 8, "carb": 8, "sugar": 2, "protein": 8, "sodium": 200, "fiber": 3}, {"name": "<PERSON><PERSON>", "serving_size_grams": 150, "calorie": 180, "fat": 12, "carb": 10, "sugar": 4, "protein": 6, "sodium": 250, "fiber": 5}, {"name": "Trancam", "serving_size_grams": 120, "calorie": 150, "fat": 10, "carb": 8, "sugar": 3, "protein": 5, "sodium": 200, "fiber": 4}, {"name": "Plecing Kangkung", "serving_size_grams": 150, "calorie": 100, "fat": 7, "carb": 6, "sugar": 2, "protein": 3, "sodium": 250, "fiber": 4}, {"name": "<PERSON><PERSON>", "serving_size_grams": 100, "calorie": 220, "fat": 15, "carb": 8, "sugar": 2, "protein": 10, "sodium": 300, "fiber": 3}]