class UserConsumtion {
  final String id;
  final String name;
  final String pic;
  final double calorie;
  final double? fat;
  final double? carb;
  final double? sugar;
  final double? protein;
  final double? sodium;
  final double? fiber;

  // user consumption info
  final String? uid;
  final DateTime? ts;

  UserConsumtion({
    required this.id,
    required this.name,
    required this.pic,
    required this.calorie,
    required this.fat,
    required this.carb,
    required this.sugar,
    required this.protein,
    required this.sodium,
    required this.fiber,
    required this.uid,
    required this.ts,
  });

  factory UserConsumtion.fromJson(Map<String, dynamic> json) {
    return UserConsumtion(
      id: json['id'],
      name: json['name'],
      pic: json['pic'],
      calorie: json['calorie'],
      fat: json['fat'],
      carb: json['carb'],
      sugar: json['sugar'],
      protein: json['protein'],
      sodium: json['sodium'],
      fiber: json['fiber'],
      uid: json['uid'],
      ts: DateTime.fromMillisecondsSinceEpoch(json['ts']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'pic': pic,
      'calorie': calorie,
      'fat': fat,
      'carb': carb,
      'sugar': sugar,
      'protein': protein,
      'sodium': sodium,
      'fiber': fiber,
      'uid': uid,
      'ts': ts?.millisecondsSinceEpoch,
    };
  }
}
